{"ast": null, "code": "import { findAdminPageAPI,\n// 导入查询管理员信息的 API\naddAdminAPI,\n// 导入新增管理员的 API\nmodifyAdminAPI,\n// 导入修改管理员信息的 API\nremoveAdminAPI // 导入删除管理员的 API\n} from \"@/api/admin\";\nexport default {\n  data() {\n    return {\n      list: null,\n      // 管理员列表数据\n      listLoading: true,\n      // 列表加载状态\n      pageSize: 10,\n      // 每页显示条数\n      pageNum: 1,\n      // 当前页码\n      total: 0,\n      // 总条数\n      searchForm: {},\n      // 搜索表单数据\n      dialogTitle: \"添加\",\n      // 弹框标题，默认为添加\n      dialogFormVisible: false,\n      // 弹框显示状态\n      dialogForm: {},\n      // 弹框表单数据\n      genderOptions: [\n      // 性别选项\n      {\n        label: '男',\n        value: 1\n      }, {\n        label: '女',\n        value: 2\n      }]\n    };\n  },\n  created() {\n    this.fetchData();\n  },\n  methods: {\n    handleSizeChange(val) {\n      //每页显示条数改变\n      this.pageSize = val;\n      this.fetchData();\n    },\n    handleCurrentChange(val) {\n      //当前页码改变\n      this.pageNum = val;\n      this.fetchData();\n    },\n    handleSearch() {\n      //点击搜索按钮\n      this.fetchData();\n    },\n    async fetchData() {\n      //查询数据\n      this.listLoading = true;\n      let response = await findAdminPageAPI(this.pageNum, this.pageSize, this.searchForm);\n      this.list = response.data.records;\n      this.total = response.data.total;\n      this.listLoading = false;\n    },\n    handleAdd() {\n      //点击新增按钮-显示弹框\n      this.dialogTitle = \"新增\";\n      this.dialogFormVisible = true;\n      this.dialogForm = {\n        gender: 1\n      };\n    },\n    handleEdit(row) {\n      //点击修改按钮-显示弹框\n      this.dialogTitle = \"修改\";\n      this.dialogFormVisible = true;\n      this.dialogForm = {\n        ...row\n      };\n    },\n    async dialogConfirm() {\n      //点击弹框确定按钮\n      let res = null;\n      if (this.dialogTitle == \"新增\") {\n        res = await addAdminAPI(this.dialogForm);\n      } else {\n        res = await modifyAdminAPI(this.dialogForm);\n      }\n      if (res.flag) {\n        this.dialogFormVisible = false;\n      }\n      this.$message({\n        message: res.message,\n        type: res.flag ? \"success\" : \"error\"\n      });\n      this.fetchData();\n    },\n    async deleteCofirm(row) {\n      //点击确定删除按钮\n      this.pageNum = 1;\n      const res = await removeAdminAPI(row.id);\n      this.$message({\n        message: res.message,\n        type: res.flag ? \"success\" : \"error\"\n      });\n      this.fetchData();\n    }\n  }\n};", "map": {"version": 3, "names": ["findAdminPageAPI", "addAdminAPI", "modifyAdminAPI", "removeAdminAPI", "data", "list", "listLoading", "pageSize", "pageNum", "total", "searchForm", "dialogTitle", "dialogFormVisible", "dialogForm", "genderOptions", "label", "value", "created", "fetchData", "methods", "handleSizeChange", "val", "handleCurrentChange", "handleSearch", "response", "records", "handleAdd", "gender", "handleEdit", "row", "dialogConfirm", "res", "flag", "$message", "message", "type", "deleteCofirm", "id"], "sources": ["src/views/Admin.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-card class=\"box-table\">\r\n      <el-form :inline=\"true\" class=\"demo-form-inline\" :model=\"searchForm\">\r\n        <el-form-item label=\"账号\">\r\n          <el-input placeholder=\"请输入账号\" v-model=\"searchForm.username\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\">\r\n          <el-input placeholder=\"请输入姓名\" v-model=\"searchForm.name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"handleAdd\">新增</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n      <el-table v-loading=\"listLoading\" :data=\"list\" element-loading-text=\"Loading\" border fit highlight-current-row\r\n        size=\"small\">\r\n        <el-table-column align=\"center\" label=\"ID\" prop=\"id\"></el-table-column>\r\n        <el-table-column align=\"center\" label=\"账号\" prop=\"username\"></el-table-column>\r\n        <el-table-column align=\"center\" label=\"姓名\" prop=\"name\"></el-table-column>\r\n        <el-table-column align=\"center\" label=\"性别\" prop=\"gender\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag type=\"success\" v-if=\"scope.row.gender == 1\">男</el-tag>\r\n            <el-tag type=\"danger\" v-else>女</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button @click=\"handleEdit(scope.row)\" type=\"text\" size=\"small\">编辑</el-button>\r\n            <el-popconfirm title=\"确定删除吗？（逻辑删除，数据不会真正丢失）\" @confirm=\"deleteCofirm(scope.row)\">\r\n              <el-button type=\"text\" slot=\"reference\" size=\"small\">删除</el-button>\r\n            </el-popconfirm>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination class=\"page-box\" background @size-change=\"handleSizeChange\" @current-change=\"handleCurrentChange\"\r\n        :current-page=\"pageNum\" :page-sizes=\"[10, 20]\" :page-size=\"pageSize\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" :total=\"total\">\r\n      </el-pagination>\r\n    </el-card>\r\n    <!-- 添加/修改的弹框 -->\r\n    <el-dialog :title=\"dialogTitle\" :visible.sync=\"dialogFormVisible\">\r\n      <el-form label-position=\"left\" :model=\"dialogForm\" label-width=\"80px\">\r\n        <el-form-item label=\"账号\">\r\n          <el-input placeholder=\"请输入账号\" v-model=\"dialogForm.username\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"密码\">\r\n          <el-input type=\"password\" placeholder=\"请输入密码\" v-model=\"dialogForm.password\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\">\r\n          <el-input placeholder=\"请输入姓名\" v-model=\"dialogForm.name\" autocomplete=\"off\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"性别\">\r\n          <el-select v-model=\"dialogForm.gender\" placeholder=\"请选择\">\r\n            <el-option v-for=\"item in genderOptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"dialogConfirm\">确 定</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  findAdminPageAPI, // 导入查询管理员信息的 API\r\n  addAdminAPI, // 导入新增管理员的 API\r\n  modifyAdminAPI, // 导入修改管理员信息的 API\r\n  removeAdminAPI, // 导入删除管理员的 API\r\n} from \"@/api/admin\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      list: null, // 管理员列表数据\r\n      listLoading: true, // 列表加载状态\r\n      pageSize: 10, // 每页显示条数\r\n      pageNum: 1, // 当前页码\r\n      total: 0, // 总条数\r\n      searchForm: {}, // 搜索表单数据\r\n      dialogTitle: \"添加\", // 弹框标题，默认为添加\r\n      dialogFormVisible: false, // 弹框显示状态\r\n      dialogForm: {}, // 弹框表单数据\r\n      genderOptions: [ // 性别选项\r\n        {\r\n          label: '男',\r\n          value: 1\r\n        },\r\n        {\r\n          label: '女',\r\n          value: 2\r\n        }\r\n      ],\r\n    };\r\n  },\r\n  created() {\r\n    this.fetchData();\r\n  },\r\n  methods: {\r\n    handleSizeChange(val) {//每页显示条数改变\r\n      this.pageSize = val;\r\n      this.fetchData();\r\n    },\r\n    handleCurrentChange(val) {//当前页码改变\r\n      this.pageNum = val;\r\n      this.fetchData();\r\n    },\r\n    handleSearch() {//点击搜索按钮\r\n      this.fetchData();\r\n    },\r\n    async fetchData() {//查询数据\r\n      this.listLoading = true;\r\n      let response = await findAdminPageAPI(\r\n        this.pageNum,\r\n        this.pageSize,\r\n        this.searchForm\r\n      );\r\n      this.list = response.data.records;\r\n      this.total = response.data.total;\r\n      this.listLoading = false;\r\n    },\r\n    handleAdd() {//点击新增按钮-显示弹框\r\n      this.dialogTitle = \"新增\";\r\n      this.dialogFormVisible = true;\r\n      this.dialogForm = {gender: 1};\r\n    },\r\n    handleEdit(row) {//点击修改按钮-显示弹框\r\n      this.dialogTitle = \"修改\";\r\n      this.dialogFormVisible = true;\r\n      this.dialogForm = { ...row };\r\n    },\r\n    async dialogConfirm() {//点击弹框确定按钮\r\n      let res = null;\r\n      if (this.dialogTitle == \"新增\") {\r\n        res = await addAdminAPI(this.dialogForm);\r\n      } else {\r\n        res = await modifyAdminAPI(this.dialogForm);\r\n      }\r\n      if (res.flag) {\r\n        this.dialogFormVisible = false;\r\n      }\r\n      this.$message({\r\n        message: res.message,\r\n        type: res.flag ? \"success\" : \"error\",\r\n      });\r\n      this.fetchData();\r\n    },\r\n    async deleteCofirm(row) {//点击确定删除按钮\r\n      this.pageNum = 1;\r\n      const res = await removeAdminAPI(row.id);\r\n      this.$message({\r\n        message: res.message,\r\n        type: res.flag ? \"success\" : \"error\",\r\n      });\r\n      this.fetchData();\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"less\" scoped>\r\n.page-box {\r\n  margin-top: 20px;\r\n}\r\n</style>\r\n"], "mappings": "AAoEA,SACAA,gBAAA;AAAA;AACAC,WAAA;AAAA;AACAC,cAAA;AAAA;AACAC,cAAA;AAAA,OACA;AAEA;EACAC,KAAA;IACA;MACAC,IAAA;MAAA;MACAC,WAAA;MAAA;MACAC,QAAA;MAAA;MACAC,OAAA;MAAA;MACAC,KAAA;MAAA;MACAC,UAAA;MAAA;MACAC,WAAA;MAAA;MACAC,iBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,aAAA;MAAA;MACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,iBAAAC,GAAA;MAAA;MACA,KAAAd,QAAA,GAAAc,GAAA;MACA,KAAAH,SAAA;IACA;IACAI,oBAAAD,GAAA;MAAA;MACA,KAAAb,OAAA,GAAAa,GAAA;MACA,KAAAH,SAAA;IACA;IACAK,aAAA;MAAA;MACA,KAAAL,SAAA;IACA;IACA,MAAAA,UAAA;MAAA;MACA,KAAAZ,WAAA;MACA,IAAAkB,QAAA,SAAAxB,gBAAA,CACA,KAAAQ,OAAA,EACA,KAAAD,QAAA,EACA,KAAAG,UACA;MACA,KAAAL,IAAA,GAAAmB,QAAA,CAAApB,IAAA,CAAAqB,OAAA;MACA,KAAAhB,KAAA,GAAAe,QAAA,CAAApB,IAAA,CAAAK,KAAA;MACA,KAAAH,WAAA;IACA;IACAoB,UAAA;MAAA;MACA,KAAAf,WAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,UAAA;QAAAc,MAAA;MAAA;IACA;IACAC,WAAAC,GAAA;MAAA;MACA,KAAAlB,WAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,UAAA;QAAA,GAAAgB;MAAA;IACA;IACA,MAAAC,cAAA;MAAA;MACA,IAAAC,GAAA;MACA,SAAApB,WAAA;QACAoB,GAAA,SAAA9B,WAAA,MAAAY,UAAA;MACA;QACAkB,GAAA,SAAA7B,cAAA,MAAAW,UAAA;MACA;MACA,IAAAkB,GAAA,CAAAC,IAAA;QACA,KAAApB,iBAAA;MACA;MACA,KAAAqB,QAAA;QACAC,OAAA,EAAAH,GAAA,CAAAG,OAAA;QACAC,IAAA,EAAAJ,GAAA,CAAAC,IAAA;MACA;MACA,KAAAd,SAAA;IACA;IACA,MAAAkB,aAAAP,GAAA;MAAA;MACA,KAAArB,OAAA;MACA,MAAAuB,GAAA,SAAA5B,cAAA,CAAA0B,GAAA,CAAAQ,EAAA;MACA,KAAAJ,QAAA;QACAC,OAAA,EAAAH,GAAA,CAAAG,OAAA;QACAC,IAAA,EAAAJ,GAAA,CAAAC,IAAA;MACA;MACA,KAAAd,SAAA;IACA;EACA;AACA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}