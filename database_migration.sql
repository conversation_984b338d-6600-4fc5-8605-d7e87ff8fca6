-- 为所有表添加逻辑删除字段
-- deleted: 0-未删除, 1-已删除

-- 1. admin表
ALTER TABLE admin ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 2. member表
ALTER TABLE member ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 3. category表
ALTER TABLE category ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 4. room表
ALTER TABLE room ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 5. appointment表
ALTER TABLE appointment ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 6. orders表
ALTER TABLE orders ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 7. message表
ALTER TABLE message ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 8. notice表
ALTER TABLE notice ADD COLUMN deleted TINYINT(1) DEFAULT 0 COMMENT '逻辑删除标识(0-未删除,1-已删除)';

-- 为deleted字段添加索引以提高查询性能
CREATE INDEX idx_admin_deleted ON admin(deleted);
CREATE INDEX idx_member_deleted ON member(deleted);
CREATE INDEX idx_category_deleted ON category(deleted);
CREATE INDEX idx_room_deleted ON room(deleted);
CREATE INDEX idx_appointment_deleted ON appointment(deleted);
CREATE INDEX idx_orders_deleted ON orders(deleted);
CREATE INDEX idx_message_deleted ON message(deleted);
CREATE INDEX idx_notice_deleted ON notice(deleted);
