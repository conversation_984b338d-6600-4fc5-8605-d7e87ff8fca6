{"ast": null, "code": "import Vue from 'vue';\nimport VueRouter from 'vue-router';\nimport AdminLayout from '@/views/AdminLayout.vue';\nimport FrontLayout from '@/views/front/FrontLayout.vue';\nimport { getToken } from '@/utils/auth';\nimport store from '@/store';\nVue.use(VueRouter);\nconst routes = [{\n  path: '/',\n  redirect: \"/admin\"\n}, {\n  path: '/login',\n  name: 'Login',\n  component: () => import( /* webpackChunkName: \"welcome\" */'@/views/Login.vue')\n}, {\n  path: '/register',\n  name: 'Register',\n  component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/Register.vue')\n}, {\n  path: '/admin',\n  name: 'Admin',\n  component: AdminLayout,\n  children: [{\n    path: '',\n    name: 'Welcome',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/Welcome.vue')\n  }, {\n    path: 'admin',\n    name: 'admin',\n    component: () => import( /* webpackChunkName: \"Admin\" */'@/views/Admin.vue')\n  }, {\n    path: 'appointment',\n    name: 'appointment',\n    component: () => import( /* webpackChunkName: \"Appointment\" */'@/views/Appointment.vue')\n  }, {\n    path: 'category',\n    name: 'category',\n    component: () => import( /* webpackChunkName: \"Category\" */'@/views/Category.vue')\n  }, {\n    path: 'member',\n    name: 'member',\n    component: () => import( /* webpackChunkName: \"Member\" */'@/views/Member.vue')\n  }, {\n    path: 'message',\n    name: 'message',\n    component: () => import( /* webpackChunkName: \"Message\" */'@/views/Message.vue')\n  }, {\n    path: 'notice',\n    name: 'notice',\n    component: () => import( /* webpackChunkName: \"Notice\" */'@/views/Notice.vue')\n  }, {\n    path: 'orders',\n    name: 'orders',\n    component: () => import( /* webpackChunkName: \"Orders\" */'@/views/Orders.vue')\n  }, {\n    path: 'room',\n    name: 'room',\n    component: () => import( /* webpackChunkName: \"Room\" */'@/views/Room.vue')\n  }, {\n    path: 'adminInfo',\n    name: 'AdminInfo',\n    component: () => import( /* webpackChunkName: \"Room\" */'@/views/AdminInfo.vue')\n  }, {\n    path: 'addOrders',\n    name: 'OrdersAdd',\n    component: () => import( /* webpackChunkName: \"Room\" */'@/views/OrdersAdd.vue')\n  }]\n}, {\n  path: '/front',\n  name: 'Front',\n  component: FrontLayout,\n  //他这里是二级组件 用的组件里面还有组件\n  children: [{\n    path: '',\n    name: 'Home',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/Home.vue')\n  }, {\n    path: 'room',\n    name: 'Room',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/Room.vue')\n  }, {\n    path: 'roomDetails/:id',\n    name: 'RoomDetails',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/RoomDetails.vue')\n  }, {\n    path: 'notice',\n    name: 'Notice',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/Notice.vue')\n  }, {\n    path: 'noticeDetails/:id',\n    name: 'NoticeDetails',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/NoticeDetails.vue')\n  }, {\n    path: 'message',\n    name: 'Message',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/Message.vue')\n  }, {\n    path: 'userInfo',\n    name: 'UserInfo',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/UserInfo.vue')\n  }, {\n    path: 'appointment',\n    name: 'MyAppointment',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/MyAppointment.vue')\n  }, {\n    path: 'orders',\n    name: 'MyOrders',\n    component: () => import( /* webpackChunkName: \"welcome\" */'@/views/front/MyOrder.vue')\n  }]\n}];\nconst router = new VueRouter({\n  routes\n});\n\n//无需登录的页面\nconst whiteList = [\n//普通路由\n\"/login\", \"/register\", \"/front\", \"/front/room\", \"/front/notice\", \"/front/message\"];\nconst whiteListWithDetails = [\n//动态路由\n\"/front/roomDetails/\", \"/front/noticeDetails/\"];\n\n//路由拦截\nrouter.beforeEach((to, from, next) => {\n  console.log(to);\n  console.log(from);\n  /* 路由发生变化修改页面title */\n  if (to.meta.title) {\n    document.title = to.meta.title;\n  }\n  const isLogin = getToken();\n  if (isLogin) {\n    //登录之后直接放行\n    next();\n  } else {\n    //无需登录的页面放行\n    if (whiteList.includes(to.fullPath) || whiteList.includes(to.path) || whiteListWithDetails.some(s => to.fullPath.startsWith(s))) {\n      next();\n    } else {\n      next({\n        path: '/login'\n      });\n    }\n    ;\n  }\n  ;\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AdminLayout", "FrontLayout", "getToken", "store", "use", "routes", "path", "redirect", "name", "component", "children", "router", "whiteList", "whiteListWithDetails", "beforeEach", "to", "from", "next", "console", "log", "meta", "title", "document", "is<PERSON>ogin", "includes", "fullPath", "some", "s", "startsWith"], "sources": ["D:/project/hotel/hotely5d-web/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport VueRouter from 'vue-router'\r\nimport AdminLayout from '@/views/AdminLayout.vue'\r\nimport FrontLayout from '@/views/front/FrontLayout.vue'\r\nimport {getToken} from '@/utils/auth'\r\nimport store from '@/store'\r\nVue.use(VueRouter)\r\n\r\nconst routes = [\r\n\t{\r\n\t\tpath: '/',\r\n\t\tredirect: \"/admin\"\r\n\t},\r\n  {\r\n    path: '/login',\r\n    name: 'Login',\r\n    component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/Login.vue')\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'Register',\r\n    component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/Register.vue')\r\n  },\r\n  {\r\n    path: '/admin',\r\n    name: 'Admin',\r\n    component: AdminLayout,\r\n    children: [\r\n      {\r\n        path: '',\r\n        name: 'Welcome',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/Welcome.vue')\r\n      },\r\n      {\r\n        path: 'admin',\r\n        name: 'admin',\r\n        component: () => import(/* webpackChunkName: \"Admin\" */ '@/views/Admin.vue')\r\n      },\r\n      {\r\n        path: 'appointment',\r\n        name: 'appointment',\r\n        component: () => import(/* webpackChunkName: \"Appointment\" */ '@/views/Appointment.vue')\r\n      },\r\n      {\r\n        path: 'category',\r\n        name: 'category',\r\n        component: () => import(/* webpackChunkName: \"Category\" */ '@/views/Category.vue')\r\n      },\r\n      {\r\n        path: 'member',\r\n        name: 'member',\r\n        component: () => import(/* webpackChunkName: \"Member\" */ '@/views/Member.vue')\r\n      },\r\n      {\r\n        path: 'message',\r\n        name: 'message',\r\n        component: () => import(/* webpackChunkName: \"Message\" */ '@/views/Message.vue')\r\n      },\r\n      {\r\n        path: 'notice',\r\n        name: 'notice',\r\n        component: () => import(/* webpackChunkName: \"Notice\" */ '@/views/Notice.vue')\r\n      },\r\n      {\r\n        path: 'orders',\r\n        name: 'orders',\r\n        component: () => import(/* webpackChunkName: \"Orders\" */ '@/views/Orders.vue')\r\n      },\r\n      {\r\n        path: 'room',\r\n        name: 'room',\r\n        component: () => import(/* webpackChunkName: \"Room\" */ '@/views/Room.vue')\r\n      },\r\n      {\r\n        path: 'adminInfo',\r\n        name: 'AdminInfo',\r\n        component: () => import(/* webpackChunkName: \"Room\" */ '@/views/AdminInfo.vue')\r\n      },\r\n      {\r\n        path: 'addOrders',\r\n        name: 'OrdersAdd',\r\n        component: () => import(/* webpackChunkName: \"Room\" */ '@/views/OrdersAdd.vue')\r\n      },\r\n    ]\r\n  },\r\n  {\r\n    path: '/front',\r\n    name: 'Front',\r\n    component: FrontLayout, //他这里是二级组件 用的组件里面还有组件\r\n    children: [\r\n      {\r\n        path: '',\r\n        name: 'Home',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/Home.vue')\r\n      },\r\n      {\r\n        path: 'room',\r\n        name: 'Room',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/Room.vue')\r\n      },\r\n      {\r\n        path: 'roomDetails/:id',\r\n        name: 'RoomDetails',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/RoomDetails.vue')\r\n      },\r\n      {\r\n        path: 'notice',\r\n        name: 'Notice',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/Notice.vue')\r\n      },\r\n      {\r\n        path: 'noticeDetails/:id',\r\n        name: 'NoticeDetails',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/NoticeDetails.vue')\r\n      },\r\n      {\r\n        path: 'message',\r\n        name: 'Message',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/Message.vue')\r\n      },\r\n      {\r\n        path: 'userInfo',\r\n        name: 'UserInfo',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/UserInfo.vue')\r\n      },\r\n      {\r\n        path: 'appointment',\r\n        name: 'MyAppointment',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/MyAppointment.vue')\r\n      },\r\n      {\r\n        path: 'orders',\r\n        name: 'MyOrders',\r\n        component: () => import(/* webpackChunkName: \"welcome\" */ '@/views/front/MyOrder.vue')\r\n      },\r\n    ]\r\n  }\r\n\r\n]\r\n\r\nconst router = new VueRouter({\r\n  routes\r\n})\r\n\r\n//无需登录的页面\r\nconst whiteList = [//普通路由\r\n  \"/login\",\r\n  \"/register\",\r\n  \"/front\",\r\n  \"/front/room\",\r\n  \"/front/notice\",\r\n  \"/front/message\"\r\n]\r\nconst whiteListWithDetails = [//动态路由\r\n  \"/front/roomDetails/\",\r\n  \"/front/noticeDetails/\",\r\n]\r\n\r\n//路由拦截\r\nrouter.beforeEach((to, from, next) => {\r\n\tconsole.log(to);\r\n\tconsole.log(from);\r\n\t/* 路由发生变化修改页面title */\r\n\tif (to.meta.title) {\r\n\t  document.title = to.meta.title\r\n\t}\r\n\tconst isLogin = getToken()\r\n\tif (isLogin) { //登录之后直接放行\r\n\t\tnext()\r\n\t} else {\r\n\t\t//无需登录的页面放行\r\n\t\tif (whiteList.includes(to.fullPath) || whiteList.includes(to.path) || whiteListWithDetails.some(s => to.fullPath.startsWith(s))) {\r\n\t\t\tnext();\r\n\t\t} else {\r\n\t\t\tnext({\r\n\t\t\t\tpath: '/login'\r\n\t\t\t})\r\n\t\t};\r\n\t};\r\n})\r\n\r\nexport default router\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,WAAW,MAAM,+BAA+B;AACvD,SAAQC,QAAQ,QAAO,cAAc;AACrC,OAAOC,KAAK,MAAM,SAAS;AAC3BL,GAAG,CAACM,GAAG,CAACL,SAAS,CAAC;AAElB,MAAMM,MAAM,GAAG,CACd;EACCC,IAAI,EAAE,GAAG;EACTC,QAAQ,EAAE;AACX,CAAC,EACA;EACED,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,mBAAmB;AAC/E,CAAC,EACD;EACEH,IAAI,EAAE,WAAW;EACjBE,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,4BAA4B;AACxF,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAET,WAAW;EACtBU,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,EAAE;IACRE,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,qBAAqB;EACjF,CAAC,EACD;IACEH,IAAI,EAAE,OAAO;IACbE,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,+BAAgC,mBAAmB;EAC7E,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,qCAAsC,yBAAyB;EACzF,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBE,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,kCAAmC,sBAAsB;EACnF,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdE,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,gCAAiC,oBAAoB;EAC/E,CAAC,EACD;IACEH,IAAI,EAAE,SAAS;IACfE,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,qBAAqB;EACjF,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdE,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,gCAAiC,oBAAoB;EAC/E,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdE,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,gCAAiC,oBAAoB;EAC/E,CAAC,EACD;IACEH,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,8BAA+B,kBAAkB;EAC3E,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBE,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,8BAA+B,uBAAuB;EAChF,CAAC,EACD;IACEH,IAAI,EAAE,WAAW;IACjBE,IAAI,EAAE,WAAW;IACjBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,8BAA+B,uBAAuB;EAChF,CAAC;AAEL,CAAC,EACD;EACEH,IAAI,EAAE,QAAQ;EACdE,IAAI,EAAE,OAAO;EACbC,SAAS,EAAER,WAAW;EAAE;EACxBS,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,EAAE;IACRE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,wBAAwB;EACpF,CAAC,EACD;IACEH,IAAI,EAAE,MAAM;IACZE,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,wBAAwB;EACpF,CAAC,EACD;IACEH,IAAI,EAAE,iBAAiB;IACvBE,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,+BAA+B;EAC3F,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdE,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,0BAA0B;EACtF,CAAC,EACD;IACEH,IAAI,EAAE,mBAAmB;IACzBE,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,iCAAiC;EAC7F,CAAC,EACD;IACEH,IAAI,EAAE,SAAS;IACfE,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,2BAA2B;EACvF,CAAC,EACD;IACEH,IAAI,EAAE,UAAU;IAChBE,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,4BAA4B;EACxF,CAAC,EACD;IACEH,IAAI,EAAE,aAAa;IACnBE,IAAI,EAAE,eAAe;IACrBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,iCAAiC;EAC7F,CAAC,EACD;IACEH,IAAI,EAAE,QAAQ;IACdE,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEA,CAAA,KAAM,MAAM,EAAC,iCAAkC,2BAA2B;EACvF,CAAC;AAEL,CAAC,CAEF;AAED,MAAME,MAAM,GAAG,IAAIZ,SAAS,CAAC;EAC3BM;AACF,CAAC,CAAC;;AAEF;AACA,MAAMO,SAAS,GAAG;AAAC;AACjB,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,aAAa,EACb,eAAe,EACf,gBAAgB,CACjB;AACD,MAAMC,oBAAoB,GAAG;AAAC;AAC5B,qBAAqB,EACrB,uBAAuB,CACxB;;AAED;AACAF,MAAM,CAACG,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACrCC,OAAO,CAACC,GAAG,CAACJ,EAAE,CAAC;EACfG,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;EACjB;EACA,IAAID,EAAE,CAACK,IAAI,CAACC,KAAK,EAAE;IACjBC,QAAQ,CAACD,KAAK,GAAGN,EAAE,CAACK,IAAI,CAACC,KAAK;EAChC;EACA,MAAME,OAAO,GAAGrB,QAAQ,CAAC,CAAC;EAC1B,IAAIqB,OAAO,EAAE;IAAE;IACdN,IAAI,CAAC,CAAC;EACP,CAAC,MAAM;IACN;IACA,IAAIL,SAAS,CAACY,QAAQ,CAACT,EAAE,CAACU,QAAQ,CAAC,IAAIb,SAAS,CAACY,QAAQ,CAACT,EAAE,CAACT,IAAI,CAAC,IAAIO,oBAAoB,CAACa,IAAI,CAACC,CAAC,IAAIZ,EAAE,CAACU,QAAQ,CAACG,UAAU,CAACD,CAAC,CAAC,CAAC,EAAE;MAChIV,IAAI,CAAC,CAAC;IACP,CAAC,MAAM;MACNA,IAAI,CAAC;QACJX,IAAI,EAAE;MACP,CAAC,CAAC;IACH;IAAC;EACF;EAAC;AACF,CAAC,CAAC;AAEF,eAAeK,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}